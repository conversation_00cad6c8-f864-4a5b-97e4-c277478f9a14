{"name": "vite", "private": true, "version": "0.0.0", "scripts": {"dev": "vite", "build:xdh": "vite build --mode production-xdh", "build:test": "vite build --mode production-test", "build:yyt": "vite build --mode production-yyt", "build:jkh": "vite build --mode production-jkh", "build:wwqb": "vite build --mode production-wwqb", "build:ymt": "vite build --mode production-ymt", "build:cyd": "vite build --mode production-cyd", "build:ymh": "vite build --mode production-ymh", "build:ayqb": "vite build --mode production-ayqb"}, "dependencies": {"axios": "^1.6.7", "vant": "^4.0.0", "ts-md5": "^1.3.1", "vue": "^3.2.45", "vue-i18n": "^9.10.1", "vue-router": "^4.3.0"}, "devDependencies": {"@vant/auto-import-resolver": "^1.0.1", "@vitejs/plugin-vue": "^4.3.4", "less": "^4.1.3", "unplugin-vue-components": "^0.25.2", "vite": "^4.5.3"}}