import { createRouter, createWebHashHistory } from "vue-router";

const router = createRouter({
  history: createWebHashHistory(),
  routes: [
    {
      path: '/',
      name: 'home',
      component: () => import('../views/home/<USER>'),
      meta: {
        keepAlive: false
      },
    },
    {
      path: '/privacy',
      name: 'privacy',
      component: () => import('../views/privacy/index.vue'),
      meta: {
        keepAlive: false
      },
    },
    {
      path: '/downloadios',
      name: 'downloadios',
      component: () => import("../views/downloadios/index.vue"),
      alias: '/downloadios',
      meta: {
        title: 'downloadios'
      }
    }
  ]
})

export default router;