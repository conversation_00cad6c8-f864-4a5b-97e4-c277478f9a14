<template>
  <router-view v-slot="{ Component }" :key="$route.fullPath" >
    <keep-alive>
      <component :is="Component" :key="$route.name"  v-if="$route.meta.keepAlive"/>
    </keep-alive>
    <component :is="Component" :key="$route.name"  v-if="!$route.meta.keepAlive"/>
  </router-view> 
</template>
<script>

export default {
  data() {
    return {

    };
  },

  mounted(){
    document.title = import.meta.env.VITE_APP_NAME;
  },

  methods: {
    
  },
};
</script>

<style lang="less">
body {
  font-size: 16px;
  background-color: #f8f8f8;
  -webkit-font-smoothing: antialiased;
}
</style>
