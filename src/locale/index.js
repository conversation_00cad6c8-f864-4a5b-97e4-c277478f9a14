import { createI18n } from 'vue-i18n'
import zh from './lang/zh'

let lang = "zh" //localStorage.getItem('lang')
// const browserLang =  navigator.language.slice(0, 2);
// if(browserLang == 'en' || browserLang == 'th' || browserLang == 'ar'){
//    lang = browserLang;
// }
// if(!lang){
//    lang = 'en'
// }
localStorage.setItem('lang', lang)

const i18n = createI18n({
   locale: lang,
   legacy: false,
   messages:{
      zh,
   }
});

export default i18n;