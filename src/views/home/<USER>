<template>
  <div style="height: 100vh; width: 100%;position:relative;background-color: #F6F6F6;">

    <van-loading v-show="loading" class="loading" size="40" type="circular" color="#CDA573"></van-loading>

    <div style="height: 100vh; width: 100%;position:absolute;background-color: #F6F6F6;">
      <div style="height: 400px;width: 100%;position: absolute;">
      </div>

      <img src="../../assets/images/home_main_bg.png" alt="" style="width: 100%;height: 200px;position: absolute;">

      <div
        style="height: 100vh;width: 100%;padding:15px;position: absolute;flex-direction: column;box-sizing: border-box;">

        <div style="width: 100%;display: flex;flex-direction: row;margin-top: 10px;align-items: center;">
          <img :src="icon" alt="" style="width: 25px;height: 25px;">
           <span style="font-size: 14px;color: #000000;margin-left: 5px;font-weight: 900;">{{productName}}</span>
        </div>

        <div
          style="width: 100%;margin-top: 10px;padding: 15px;box-sizing: border-box;border-radius:10px;background-color: white;"
          class="flex-v-center common_shadow">
          <span style="font-size: 15px;color: #333333;margin-top: 10px;">最高可借额度(元)</span>
          <div
            style="width: 100%;display: flex;flex-direction: column;align-items: center;position: relative;margin-top: 10px;">
            <div
              style="position: absolute;width: 100%;display: flex;flex-direction: column;align-items: center;top: 10px;">
              <img src="../../assets/images/main_amount_bg.png" alt="" style="width:190px;height:60px;">
            </div>
            <span style="font-size: 50px;color: #333333;font-weight: bolder;font-family: 'maintv2';">200,000</span>
          </div>
          <div class="flex-h-center" style="width: 100%;margin-top: 20px;">
            <div style="flex: 1;" class="flex-v-center">
              <span style="font-size: 14px;color: #666666;font-weight: 700;">12期</span>
              <span style="font-size: 12px;color: #999999;font-weight: 600;">最长分期</span>
            </div>
            <div style="flex: 1;" class="flex-v-center">
              <span style="font-size: 14px;color: #666666;font-weight: 700;">3分钟</span>
              <span style="font-size: 12px;color: #999999;font-weight: 600;">最长放款时间</span>
            </div>
            <div style="flex: 1;" class="flex-v-center">
              <span style="font-size: 14px;color: #666666;font-weight: 700;">15%起(IRR)</span>
              <span style="font-size: 12px;color: #999999;font-weight: 600;">年化利率低至</span>
            </div>
          </div>
        </div>

        <div
          style="width: 100%;margin-top: 20px;padding: 20px;box-sizing: border-box;border-radius:10px;background-color: white;"
          class="flex-v-center common_shadow">

          <div
            style="width: 100%;height: 50px;display: flex;border-radius:6px;background-color: #FAF6F1;align-items: center;">
            <img src="../../assets/images/phone.png" alt="" style="width:22px;height:22px;margin-left: 15px;">
            <input type="tel" v-model="phoneNumber" placeholder="请输入您的手机号" maxlength="11"
              style="height: 100%;border-width: 0px;padding: 0px 5px;font-size: 16px;color: #000000;flex: 1;outline: none;background-color: #FAF6F1;border-radius: 6px;">
          </div>

          <div @click="clickGetOtp"
            style="width: 100%;height: 50px;border-radius: 35px;background-color: #000000;margin-top: 20px;display: flex;justify-content: center;align-items: center;position: relative;">
            <span style="font-size: 17px;font-weight: 900;color: #CDA573;">查看额度</span>
          </div>

          <div style="display: flex;flex-direction: row;align-items: center;width: 100%;flex-wrap: wrap;">
            <div style="padding: 10px 5px 10px 10px" @click="clickAgree">
              <img :src="checkAgree ? imageCheckYes : imageCheckNot" alt="" style="width:14px;height:14px;">
            </div>

            <span style="color: #999999;font-size: 14px;" @click="clickAgree">我已阅读并同意</span>

            <span @click="clickRegisterPrivacy" style="color: #CDA573;font-size: 14px;font-weight: 900;">《注册协议》</span>
          </div>
        </div>

        <div
          style="width: 100%;margin-top: 20px;padding: 10px;box-sizing: border-box;border-radius:10px;background-color: white;"
          class="common_shadow">

          <span style="font-size: 14px;font-weight: 700;color: #333333;margin-left: 5px;">为什选择{{ productName }}</span>

          <img src="../../assets/images/home_bottom.png" style="width: 100%;height: 120px;margin-top: 5px;" alt="">

        </div>
      </div>
    </div>

    <van-popup v-model:show="imageCodeDialogShow" round :style="{ padding: '0px', width: '80%' }"
      @closed="popClosedEvent">

      <div style="width: 100%;" class="flex-v-center">

        <div style="width: 100%;justify-content: flex-end;" class="flex-h">
          <div @click="imageCodeDialogShow = !imageCodeDialogShow" style="width: 40px;height: 40px;" class="flex-center">
            <img src="../../assets/images/close_big.png" alt="" style="width: 20px;height: 20px">
          </div>
        </div>

        <div style="width: 100%;padding: 0 20px;box-sizing: border-box;" class="flex-v-center">
          <div
            style="width: 100%;height: 50px;display: flex;border-radius:6px;background-color: #FAF6F1;align-items: center;">
            <img src="../../assets/images/phone.png" alt="" style="width:22px;height:22px;margin-left: 15px;">
            <input type="tel" v-model="phoneNumber" placeholder="请输入您的手机号" maxlength="11"
              style="height: 100%;border-width: 0px;padding: 0px 5px;font-size: 16px;color: #000000;flex: 1;outline: none;background-color: #FAF6F1;border-radius: 6px;">

            <div style="height: 50px;width: 50px;" class="flex-center" @click="clickClosePhone">
              <img src="../../assets/images/close.png" alt="" style="width: 17px;height: 17px;margin-right: 15px;">
            </div>
          </div>

          <div
            style="width: 100%;height: 50px;display: flex;border-radius:6px;background-color: #FAF6F1;align-items: center;margin-top: 15px;box-sizing: border-box;position: relative;">

            <img src="../../assets/images/code.png" alt="" style="width:22px;height:22px;margin-left: 15px;">

            <input type="tel" v-model="imageCode" placeholder="请输入验证码" maxlength="11"
              style="height: 100%;border-width: 0px;padding: 0px 5px;font-size: 16px;color: #000000;flex: 1;outline: none;background-color: #FAF6F1;border-radius: 6px;">

            <img @click="clickGetImageCode" :src="'data:image/png;base64,' + imageCodeBase64"
              style="width: 100px;height: 40px;position: absolute;right: 0px;right: 5px;border-radius: 4px;"
              alt=""></img>
          </div>

          <div style="width: 100%;justify-content: flex-end;margin-top: 10px;" class="flex-h">
            <span style="font-size: 12px;color: #666666;margin-right: 20px">点击图片刷新</span>
          </div>

          <div @click="clickImageCodeConfirm"
            style="width: 100%;height: 50px;border-radius: 35px;background-color: #000000;margin-top: 20px;display: flex;justify-content: center;align-items: center;position: relative;margin-bottom: 20px;">
            <span style="font-size: 17px;font-weight: 900;color: #CDA573;">下一步</span>
          </div>

        </div>
      </div>
    </van-popup>

    <div v-if="commonShow"
      style="position: absolute;top: 0;background-color: rgba(0, 0, 0, 0.4);width: 100%;height: 100%;color: white;">
      <div style='position:relative;padding-top:70px;font-size: 22px;text-align: center;'>
        <img style='position: absolute;top: 10px;right: 10px;width: 100px;height: 70px;' src="../../assets/images/jiantou.png" />
        <p style="font-weight: 900;color: white;margin-bottom: 10px;">请点击屏幕右上角 【 ··· 】 </p>
        <p style="color: white;">在 <img style='width: 30px;height: 30px;color: #FFFFFF'
            src="../../assets/images/diqiu.png" /> 浏览器中打开</p>
      </div>
    </div>

    <div v-if="ucShow"
      style="position: absolute;top: 0;background-color: rgba(0, 0, 0, 0.4);width: 100%;height: 100%;color: white;">
      <div style='position:relative;padding-top:40px;text-align: center;'>
        <p style="font-size: 24px;font-weight: 700;color: white;margin-bottom: 10px;">请使用Safari浏览器打开链接</p>
      </div>
    </div>
  </div>
</template>

<script>
import { useRouter } from 'vue-router';
import { ref } from "vue";
import { showToast } from "vant";
import { showDialog } from 'vant';
import { useRoute } from "vue-router";

import { Md5 } from 'ts-md5';

import imageCheckYes from '../../assets/images/check_yes.png'
import imageCheckNot from '../../assets/images/check_not.png'

export default {

  setup() {
    const route = useRoute();
    const router = useRouter();

    const loading = ref(false)
    const platformType = ref(1)
    const icon =import.meta.env.VITE_APP_ICON
    const channel = ref("")
    const guestId = ref("")
    const productName = ref("")

    const commonShow = ref(false)
    const ucShow = ref(false)

    const phoneNumber = ref()

    const privacyData = ref()


    const imageCodeBase64 = ref("")
    const imageCodeId = ref("")
    const imageCode = ref("")
    const imageCodeDialogShow = ref(false)

    const checkAgree = ref(true)

    const dataInit = async () => {
      const userAgent = navigator.userAgent.toLowerCase()
      console.log("userAgent--->" + userAgent);

      if (isIOS()) {
        platformType.value = 1
      } else if (isAndroid()) {
        platformType.value = 2
      } else {
        platformType.value = 3
      }

      var channelRoute = route.query?.channel?.toString()
      if (!channelRoute) {
        channelRoute = ""
      }

      channel.value = channelRoute

      console.log("channelRoute-->" + channelRoute);

      console.log("platformType-->" + platformType.value);

      var channelInitData = {
        channel_code: channelRoute,
        guest_id: guestId.value,
        platform_name: platformType.value,
        referrer_url: document.referrer
      }

      fetch(import.meta.env.VITE_APP_BASE_API + `/2oh9s6nb/uv`, {
        method: 'POST',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(channelInitData)
      }).then(res => {

      }).catch(err => {

      })
    }

    const initUrl = () => {
      getGuestId()

      productName.value = import.meta.env.VITE_APP_NAME;

      if (isWX()) {
        commonShow.value = true
      } else {
        commonShow.value = false
      }
      console.log("commonShow--" + commonShow.value);

      if (isIOS() && isUC()) {
        ucShow.value = true
      } else {
        ucShow.value = false
      }

      console.log("ucShow--" + ucShow.value);
    }

    const getGuestId = () => {
      fetch('https://ifconfig.me/ip', {
        method: 'GET',
      }).then(async res => {
        const ip = await res.text()
        console.log('ip-----' + ip)
        guestId.value = Md5.hashStr(ip)
        dataInit()
      }).catch(err => {
        console.log('ip---errr--')
        dataInit()
      })
    }

    const clickGetImageCode = () => {
      if (phoneNumber.value.length < 11) {
        showToast("请输入正确手机号")
        return
      }
      getImageCodeData()
    }

    const getImageCodeData = async () => {
      fetch(import.meta.env.VITE_APP_BASE_API + `/tdd9ugur/img-code`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
      }).then(async res => {
        const response = await res.json()
        console.log("res---channel--image--code--" + JSON.stringify(response));
        if (response.code == 0) {
          if (response.data?.base64Img) {
            imageCodeBase64.value = response.data?.base64Img
            imageCodeDialogShow.value = true
          }
          if (response.data?.imgId) {
            imageCodeId.value = response.data?.imgId
          }
        } else {
          const errorMsg = response.msg
          showToast(errorMsg)
        }
        loading.value = false
      }).catch(err => {
        loading.value = false
      })
    }

    const clickGetOtp = async () => {
      if(!phoneNumber.value){
        showToast("请输入正确手机号")
        return
      }
      if (phoneNumber.value.length < 11) {
        showToast("请输入正确手机号")
        return
      }
      if (!checkAgree.value) {
        showToast("请阅读并同意协议")
        return
      }

      loading.value = true
      setTimeout(() => {
        getImageCodeData()
      }, 500);
    }

    const clickImageCodeConfirm = async () => {
      if (phoneNumber.value.length < 11) {
        showToast("请输入正确手机号")
        return
      }
      if (!imageCode.value) {
        showToast("请输入正确验证码")
        return
      }

      loading.value = true
      imageCodeDialogShow.value = false

      var channelSubmitPostBean = {
        channel_code: channel.value,
        guest_id: guestId.value,
        img_code: imageCode.value,
        img_id: imageCodeId.value,
        mobile_num: phoneNumber.value
      }

      fetch(import.meta.env.VITE_APP_BASE_API + `/u3tvne0u/uv-submit`, {
        method: 'POST',
        body: JSON.stringify(channelSubmitPostBean),
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
      }).then(async res => {
        const response = await res.json()
        console.log("res---channel--confirm---" + JSON.stringify(response));
        var download = route.query?.download?.toString() || ""
        if (response.code == 0) {
          if(download!="1"){
              window.location.href =  import.meta.env.VITE_H5_URL
          } else {
            if (response.data) {
            jumpDownLoad(response.data)
          }
          }
        } else {
          const errorMsg = response.msg
          showToast(errorMsg)
        }
        loading.value = false
      }).catch(err => {
        loading.value = false
      })
    }

    const clickAgree = () => {
      checkAgree.value = !checkAgree.value
    }

    const jumpDownLoad = (data) => {
      console.log("jumpDownLoad-->");
      var android = data.android_url
      var ios = data.ios_url
      if (!android) {
        android = ""
      }
      if (!ios) {
        ios = ""
      }

      console.log("platformType.value-jumpDownLoad->" + platformType.value);

      if (platformType.value == 1) {
        console.log("ios---url--"+ios);
        router.push({
            path: '/downloadios',
            query: {
              url: encodeURIComponent(ios)
            }
        })
      } else {
        window.location.href = android
      }
    }

    const clickRegisterPrivacy = () => {
      router.push({
        name: 'privacy',
      })
    }

    const clickClosePhone = () => {
      phoneNumber.value = ""
    }

    const isWX = () => {
      var u = navigator.userAgent
      return u.indexOf("MicroMessenger") > 0;
    }

    const isIOS = () => {
      var u = navigator.userAgent
      return !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/);
    }

    const isAndroid = () => {
      var u = navigator.userAgent
      return u.indexOf('Android') > -1 || u.indexOf('Linux') > -1
    }

    const isUC = () => {
      var u = navigator.userAgent
      return u.indexOf("UCBrowser") > 0;
    }

    const popClosedEvent = () => {
      imageCode.value = ""
    }

    return {
      popClosedEvent,
      loading,
      isWX,
      isIOS,
      isAndroid,
      isUC,
      getGuestId,
      channel,
      platformType,
      initUrl,
      dataInit,
      productName,
      ucShow,
      commonShow,
      clickGetImageCode,
      imageCodeBase64,
      imageCodeId,
      imageCode,
      imageCodeDialogShow,
      checkAgree,
      clickAgree,
      clickRegisterPrivacy,
      clickGetOtp,
      imageCheckNot,
      imageCheckYes,
      phoneNumber,
      clickClosePhone,
      clickImageCodeConfirm,
      icon

    }
  },

  mounted() {
    this.initUrl()
  },

  components: {

  }
}
</script>

<style lang="less" scoped>
@import "../../styles/common.less";

.van-floating-bubble {
  background: @primaryColor
}

input::-webkit-input-placeholder {
  color: #CCCCCC;
}
</style>