<template>
  <div style="height: 100vh; width: 100%;position:relative;background-color: #F6F6F6;">
    <van-loading v-show="loading" class="loading" size="40" type="circular" color="#CDA573"></van-loading>

    <img src="../../assets/images/download_bg.png" style="width: 100%;height: 100%;position: absolute;" alt="">

    <div
      style="width: 100%;height: 100%;position: absolute;display: flex;flex-direction: column;align-items: center;padding: 0px 30px;box-sizing: border-box;">
      <img :src="icon" style="width: 110px;height: 110px;margin-top: 80px;border-radius: 15px;"
        alt="">

      <div style="width: 100%;padding: 0px 20px;box-sizing: border-box;">
        <div
        @click="clickDownload"
          style="width: 100%;height: 60px;display: flex;flex-direction: row;justify-content: center;align-items: center;background-color:#1C2D5C;border-radius: 30px;margin-top: 50px;">
          <div style="display: flex;flex-direction: row;align-items: center;">
            <img src="../../assets/images/iphone.png" style="width: 30px;height: 30px;" alt="">
            <span style="font-size: 16px;color: #FFFFFF;margin-left: 10px;">iphone下载</span>
          </div>
        </div>
      </div>

      <img src="../../assets/images/img5.png" style="width: 180px;height: 28px;margin-top: 80px;" alt="">

      <div style="display: flex;flex-direction: row;width: 100%;align-items: center;margin-top: 60px;">
        <span style="font-size: 25px;color: black;">01</span>
        <span style="font-size: 18px;color: #000000;margin-left: 20px;">打开手机设置APP</span>
      </div>
      <div style="display: flex;flex-direction: row;width: 100%;align-items: center;margin-top: 20px;">
        <span style="font-size: 25px;color: black;">02</span>
        <span style="font-size: 18px;color: #000000;margin-left: 20px;">找出点击通用</span>
      </div>
      <div style="display: flex;flex-direction: row;width: 100%;align-items: center;margin-top: 20px;">
        <span style="font-size: 25px;color: black;">03</span>
        <span style="font-size: 18px;color: #000000;margin-left: 20px;">然后找到点击设备管理</span>
      </div>
      <div style="display: flex;flex-direction: row;width: 100%;align-items: center;margin-top: 20px;">
        <span style="font-size: 25px;color: black;">04</span>
        <span style="font-size: 18px;color: #000000;margin-left: 20px;">找到企业级应用，点击信任证书</span>
      </div>
    </div>
  </div>
</template>

<script>
import { useRouter } from 'vue-router';
import { ref } from "vue";
import { showToast } from "vant";
import { showDialog } from 'vant';
import { useRoute } from "vue-router";

export default {

  setup() {
    const icon= ref(import.meta.env.VITE_APP_ICON)
    const route = useRoute();
    const router = useRouter();

    const loading = ref(false)

    const iosUrl = ref()

    const clickDownload=()=>{
      if(iosUrl.value){
        window.location.href = decodeURIComponent(iosUrl.value)
      }
    }

    
    return {
      loading,
      iosUrl,
      icon,

      clickDownload
    }
  },

  mounted() {
    const aUrl = this.$route.query.url?.toString()
    console.log("aUrl---" + aUrl);
    if (aUrl) {
      this.iosUrl = aUrl
      window.location.href = decodeURIComponent(aUrl)
    }
  },

  components: {

  }
}
</script>

<style lang="less" scoped>
@import "../../styles/common.less";

.van-floating-bubble {
  background: @primaryColor
}

input::-webkit-input-placeholder {
  color: #CCCCCC;
}
</style>