<template>
  <div style="height: 100vh; width: 100%;position:relative;background-color: #F6F6F6;">
    <van-loading v-show="loading" class="loading" size="40" type="circular" color="#CDA573"></van-loading>
    <van-nav-bar
  :title="privacyData.title"
  left-text="返回"
  left-arrow
  @click-left="onClickLeft"
/>
    <div v-html="privacyData.text" style="width: 100%;padding: 0 15px;box-sizing: border-box;"></div>
  </div>
</template>

<script>
import { useRouter } from 'vue-router';
import { ref } from "vue";
import { showToast } from "vant";
import { showDialog } from 'vant';
import { useRoute } from "vue-router";

export default {

  setup() {
    const route = useRoute();
    const router = useRouter();

    const privacyData = ref({
      text:'',
      title:'注册协议'
    })

    const loading = ref(false)

    const getPrivacyPolicyData = async () => {
      loading.value = true
      fetch(import.meta.env.VITE_APP_BASE_API + `/536f139b5cdd48b590f11d7d5c773334/zcxy`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
      }).then(async res => {
        const response = await res.json()
        if (response.code == 0) {
          const privacyValue = response.data
          privacyValue.text = response.data.text.replaceAll("{{ appName }}",import.meta.env.VITE_APP_NAME)
          
          privacyData.value = privacyValue
        } else {
          showToast(response.msg)
        }
        loading.value = false
      }).catch(err => {
        loading.value = false
      })
    }

    const onClickLeft=()=>{
      router.back()
    }

    return {
      loading,
      privacyData,
      getPrivacyPolicyData,
      onClickLeft
    }
  },

  mounted() {
    this.getPrivacyPolicyData()
  },

  components: {

  }
}
</script>

<style lang="less" scoped>
@import "../../styles/common.less";

.van-floating-bubble {
  background: @primaryColor
}

input::-webkit-input-placeholder {
  color: #CCCCCC;
}
</style>